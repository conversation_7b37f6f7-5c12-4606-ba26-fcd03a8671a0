"""
Data models for the Options Manipulation Detection System
"""
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field
import uuid

class PatternType(str, Enum):
    """Types of manipulation patterns"""
    ORDER_SPOOFING = "order_spoofing"
    OPTIONS_PINNING = "options_pinning"
    GAMMA_SQUEEZE_SETUP = "gamma_squeeze_setup"
    VOLATILITY_SKEW_MANIPULATION = "volatility_skew_manipulation"

    # Deep analysis patterns
    EXPIRY_PINNING = "expiry_pinning"
    INSTITUTIONAL_FLOW = "institutional_flow"
    GAMMA_SQUEEZE = "gamma_squeeze"
    VOLATILITY_CRUSH = "volatility_crush"
    FII_FLOW = "fii_flow"
    DII_FLOW = "dii_flow"
    EXPIRY_MANIPULATION = "expiry_manipulation"
    UNUSUAL_OPTIONS_ACTIVITY = "unusual_options_activity"
    CROSS_ASSET_MANIPULATION = "cross_asset_manipulation"
    TIME_BASED_MANIPULATION = "time_based_manipulation"
    INSTITUTIONAL_FLOW_DISGUISE = "institutional_flow_disguise"

class OptionType(str, Enum):
    """Option types"""
    CALL = "CE"
    PUT = "PE"

class ConfidenceLevel(str, Enum):
    """Confidence levels for signals"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ManipulationSignal:
    """
    Represents detected manipulation pattern with ACTUAL MARKET EVIDENCE
    No more fantasy confidence scores - only measurable market microstructure data
    """
    pattern_type: PatternType
    timestamp: datetime
    symbols_affected: List[str]
    description: str

    # ACTUAL MARKET MICROSTRUCTURE EVIDENCE (The Architect's Fix #4)
    oi_change_pct_1min: float  # Open interest change % in 1 minute
    iv_spike_z_score: float    # Implied volatility spike Z-score
    bid_ask_spread_pct: float  # Bid-ask spread as % of mid price
    traded_volume_vs_avg: float # Current volume vs 20-period average
    order_book_imbalance_ratio: float # (bid_qty - ask_qty) / (bid_qty + ask_qty)

    # Price impact evidence
    price_change_bps: float    # Price change in basis points
    time_window_seconds: float # Time window of the manipulation

    # Additional metadata with defaults
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    detection_algorithm: str = ""
    raw_data: Dict[str, Any] = field(default_factory=dict)

    @property
    def manipulation_strength(self) -> ConfidenceLevel:
        """
        Calculate manipulation strength based on ACTUAL EVIDENCE, not fantasy confidence
        """
        # Evidence-based scoring using actual market data
        evidence_score = 0.0

        # OI change evidence (20% weight)
        if abs(self.oi_change_pct_1min) > 10:  # >10% OI change in 1 min
            evidence_score += 0.2
        elif abs(self.oi_change_pct_1min) > 5:  # >5% OI change
            evidence_score += 0.1

        # IV spike evidence (25% weight)
        if self.iv_spike_z_score > 3:  # >3 sigma IV spike
            evidence_score += 0.25
        elif self.iv_spike_z_score > 2:  # >2 sigma IV spike
            evidence_score += 0.15

        # Spread evidence (15% weight)
        if self.bid_ask_spread_pct > 5:  # >5% spread
            evidence_score += 0.15
        elif self.bid_ask_spread_pct > 2:  # >2% spread
            evidence_score += 0.1

        # Volume evidence (20% weight)
        if self.traded_volume_vs_avg > 5:  # 5x average volume
            evidence_score += 0.2
        elif self.traded_volume_vs_avg > 3:  # 3x average volume
            evidence_score += 0.15

        # Order book imbalance evidence (20% weight)
        if abs(self.order_book_imbalance_ratio) > 0.8:  # >80% imbalance
            evidence_score += 0.2
        elif abs(self.order_book_imbalance_ratio) > 0.6:  # >60% imbalance
            evidence_score += 0.1

        # Convert to confidence level
        if evidence_score >= 0.8:
            return ConfidenceLevel.CRITICAL
        elif evidence_score >= 0.6:
            return ConfidenceLevel.HIGH
        elif evidence_score >= 0.4:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW

class OptionsData(BaseModel):
    """Options chain data model"""
    symbol: str
    expiry_date: datetime
    strike: float
    option_type: OptionType
    
    # Pricing data
    last_price: float
    bid_price: float
    ask_price: float
    
    # Volume and OI
    volume: int
    open_interest: int
    bid_qty: int
    ask_qty: int
    
    # Greeks
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    implied_volatility: Optional[float] = None
    
    # Metadata
    timestamp: datetime
    change: Optional[float] = None
    percent_change: Optional[float] = None

class OrderBookData(BaseModel):
    """Order book data model"""
    symbol: str
    strike: float
    option_type: OptionType
    timestamp: datetime
    
    # Bid side
    bid_prices: List[float]
    bid_quantities: List[int]
    
    # Ask side
    ask_prices: List[float]
    ask_quantities: List[int]
    
    # Last trade
    last_trade_price: float
    last_trade_qty: int

class TradeData(BaseModel):
    """Individual trade data model"""
    symbol: str
    strike: float
    option_type: OptionType
    timestamp: datetime
    
    trade_price: float
    trade_qty: int
    trade_value: float
    
    # Market impact
    price_change: float
    volume_impact: float

class MarketData(BaseModel):
    """Aggregated market data"""
    symbol: str
    timestamp: datetime
    
    # Underlying data
    underlying_price: float
    underlying_change: float
    underlying_volume: int
    
    # Options summary
    total_call_oi: int
    total_put_oi: int
    total_call_volume: int
    total_put_volume: int
    
    # Market indicators
    pcr_oi: float  # Put-Call Ratio by OI
    pcr_volume: float  # Put-Call Ratio by Volume
    max_pain: Optional[float] = None
    
class DetectionResult(BaseModel):
    """Result from a detection algorithm"""
    algorithm_name: str
    execution_time: float
    signals_found: int
    signals: List[ManipulationSignal]
    errors: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class AlertConfig(BaseModel):
    """Alert configuration"""
    pattern_types: List[PatternType]
    min_confidence: float
    min_estimated_profit: float
    symbols: List[str]
    enabled: bool = True

class SystemMetrics(BaseModel):
    """System performance metrics"""
    timestamp: datetime
    
    # Data collection metrics
    data_points_collected: int
    collection_errors: int
    collection_latency_ms: float
    
    # Detection metrics
    detection_cycles_completed: int
    total_signals_generated: int
    high_confidence_signals: int
    
    # System health
    memory_usage_mb: float
    cpu_usage_percent: float
    active_connections: int
    
class APIResponse(BaseModel):
    """Standard API response model"""
    success: bool
    message: str
    data: Optional[Any] = None
    errors: List[str] = Field(default_factory=list)
    timestamp: datetime = Field(default_factory=datetime.now)

# Database models (SQLAlchemy)
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
import uuid as uuid_lib

Base = declarative_base()

class ManipulationSignalDB(Base):
    """
    Database model for manipulation signals - EVIDENCE-BASED SCHEMA
    The Architect's Fix #4: Store actual market microstructure data, not fantasy metrics
    """
    __tablename__ = "manipulation_signals"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid_lib.uuid4)
    pattern_type = Column(String(50), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    symbols_affected = Column(JSON, nullable=False)
    description = Column(Text, nullable=False)

    # ACTUAL MARKET MICROSTRUCTURE EVIDENCE (replacing confidence/estimated_profit fantasy)
    oi_change_pct_1min = Column(Float, nullable=False)  # Open interest change % in 1 minute
    iv_spike_z_score = Column(Float, nullable=False)    # Implied volatility spike Z-score
    bid_ask_spread_pct = Column(Float, nullable=False)  # Bid-ask spread as % of mid price
    traded_volume_vs_avg = Column(Float, nullable=False) # Current volume vs 20-period average
    order_book_imbalance_ratio = Column(Float, nullable=False) # (bid_qty - ask_qty) / (bid_qty + ask_qty)

    # Price impact evidence
    price_change_bps = Column(Float, nullable=False)    # Price change in basis points
    time_window_seconds = Column(Float, nullable=False) # Time window of the manipulation

    # Metadata
    detection_algorithm = Column(String(100), nullable=False)
    raw_data = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)

class OptionsDataDB(Base):
    """Database model for options data"""
    __tablename__ = "options_data"
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(20), nullable=False)
    expiry_date = Column(DateTime, nullable=False)
    strike = Column(Float, nullable=False)
    option_type = Column(String(2), nullable=False)
    
    last_price = Column(Float)
    bid_price = Column(Float)
    ask_price = Column(Float)
    volume = Column(Integer)
    open_interest = Column(Integer)
    bid_qty = Column(Integer)
    ask_qty = Column(Integer)
    
    delta = Column(Float)
    gamma = Column(Float)
    theta = Column(Float)
    vega = Column(Float)
    implied_volatility = Column(Float)
    
    timestamp = Column(DateTime, nullable=False)
    change = Column(Float)
    percent_change = Column(Float)

class SystemMetricsDB(Base):
    """Database model for system metrics"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, nullable=False)
    
    data_points_collected = Column(Integer)
    collection_errors = Column(Integer)
    collection_latency_ms = Column(Float)
    
    detection_cycles_completed = Column(Integer)
    total_signals_generated = Column(Integer)
    high_confidence_signals = Column(Integer)
    
    memory_usage_mb = Column(Float)
    cpu_usage_percent = Column(Float)
    active_connections = Column(Integer)
