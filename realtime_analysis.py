#!/usr/bin/env python3
"""
Real-time Market Analysis Module
===============================

Provides continuous real-time analysis of NSE options markets with:
- Live pattern detection and institutional flow analysis
- Dynamic risk assessment and regime classification
- Real-time trading signal generation
- Continuous performance monitoring

This module runs alongside the main detection engine to provide
instant market insights and trading opportunities.
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from deep_analysis_engine import deep_analysis_engine, AnalysisDepth, DeepAnalysisResult
from data_collectors.multi_source_collector import multi_source_collector
from models.data_models import OptionsData
from utils.enhanced_logging import enhanced_logger
from config.settings import settings

logger = logging.getLogger(__name__)

@dataclass
class RealTimeInsight:
    """Real-time market insight"""
    timestamp: datetime
    insight_type: str
    message: str
    confidence: float
    urgency: str  # LOW, MEDIUM, HIGH, CRITICAL
    data: Dict[str, Any]

@dataclass
class MarketAlert:
    """Real-time market alert"""
    timestamp: datetime
    alert_type: str
    symbol: str
    message: str
    severity: str  # INFO, WARNING, CRITICAL
    action_required: bool
    data: Dict[str, Any]

class RealTimeAnalyzer:
    """
    Real-time market analysis engine
    """
    
    def __init__(self):
        self.logger = enhanced_logger.logger
        self.running = False
        self.analysis_interval = 30  # seconds
        self.insights_history: List[RealTimeInsight] = []
        self.alerts_history: List[MarketAlert] = []
        self.last_analysis: Optional[DeepAnalysisResult] = None
        
        # Performance tracking
        self.performance_metrics = {
            'total_analyses': 0,
            'total_patterns': 0,
            'total_opportunities': 0,
            'avg_analysis_time': 0.0,
            'success_rate': 0.0
        }
        
        # Market state tracking
        self.market_state = {
            'current_regime': 'UNKNOWN',
            'risk_level': 'HIGH',
            'trading_allowed': False,
            'dominant_flow': 'NEUTRAL',
            'vix_level': 0.0
        }
    
    async def start_realtime_analysis(self):
        """Start continuous real-time analysis"""
        try:
            self.logger.info("🚀 STARTING REAL-TIME MARKET ANALYSIS")
            self.logger.info("=" * 60)
            self.logger.info("NSE Options Real-time Analysis Engine")
            self.logger.info(f"Analysis Interval: {self.analysis_interval} seconds")
            self.logger.info("=" * 60)
            
            self.running = True
            
            while self.running:
                try:
                    # Run analysis cycle
                    await self._run_analysis_cycle()
                    
                    # Wait for next cycle
                    await asyncio.sleep(self.analysis_interval)
                    
                except Exception as e:
                    self.logger.error(f"❌ Error in analysis cycle: {str(e)}")
                    await asyncio.sleep(10)  # Short wait before retry
            
        except KeyboardInterrupt:
            self.logger.info("🛑 Real-time analysis stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Real-time analysis failed: {str(e)}")
        finally:
            await self.stop()
    
    async def stop(self):
        """Stop real-time analysis"""
        self.running = False
        self.logger.info("✅ Real-time analysis stopped")
    
    async def _run_analysis_cycle(self):
        """Run a single analysis cycle"""
        cycle_start = time.time()
        
        try:
            self.logger.info(f"🔄 Analysis Cycle {self.performance_metrics['total_analyses'] + 1}")
            
            # Collect fresh market data
            market_data = await self._collect_market_data()
            
            if not market_data:
                self.logger.warning("⚠️  No market data available")
                return
            
            # Run deep analysis
            analysis_result = await deep_analysis_engine.run_deep_analysis(
                market_data, AnalysisDepth.STANDARD
            )
            
            self.last_analysis = analysis_result
            
            # Update market state
            self._update_market_state(analysis_result)
            
            # Generate insights
            insights = self._generate_insights(analysis_result, market_data)
            self.insights_history.extend(insights)
            
            # Generate alerts
            alerts = self._generate_alerts(analysis_result, market_data)
            self.alerts_history.extend(alerts)
            
            # Update performance metrics
            self._update_performance_metrics(analysis_result, time.time() - cycle_start)
            
            # Log cycle summary
            self._log_cycle_summary(analysis_result, insights, alerts)
            
            # Show real-time dashboard
            self._show_realtime_dashboard()
            
        except Exception as e:
            self.logger.error(f"❌ Analysis cycle failed: {str(e)}")
    
    async def _collect_market_data(self) -> List[OptionsData]:
        """Collect fresh market data"""
        try:
            symbols = settings.symbols if settings.symbols else ["NIFTY", "BANKNIFTY"]
            symbols_data = await multi_source_collector.collect_all_symbols(symbols)
            
            all_data = []
            for symbol, options_list in symbols_data.items():
                all_data.extend(options_list)
            
            return all_data
            
        except Exception as e:
            self.logger.error(f"❌ Data collection failed: {str(e)}")
            return []
    
    def _update_market_state(self, result: DeepAnalysisResult):
        """Update current market state"""
        try:
            self.market_state.update({
                'current_regime': result.market_snapshot.regime.value,
                'risk_level': result.market_snapshot.risk_level,
                'trading_allowed': result.market_snapshot.trading_allowed,
                'dominant_flow': result.market_snapshot.dominant_flow,
                'vix_level': result.market_snapshot.vix_level
            })
            
        except Exception as e:
            self.logger.error(f"❌ Failed to update market state: {str(e)}")
    
    def _generate_insights(self, result: DeepAnalysisResult, market_data: List[OptionsData]) -> List[RealTimeInsight]:
        """Generate real-time insights"""
        insights = []
        
        try:
            # Market regime insight
            if result.market_snapshot.regime.value != self.market_state.get('current_regime', 'UNKNOWN'):
                insights.append(RealTimeInsight(
                    timestamp=datetime.now(),
                    insight_type="REGIME_CHANGE",
                    message=f"Market regime changed to {result.market_snapshot.regime.value}",
                    confidence=0.9,
                    urgency="HIGH",
                    data={"new_regime": result.market_snapshot.regime.value}
                ))
            
            # High confidence patterns
            high_conf_patterns = [p for p in result.detected_patterns if p.confidence > 0.8]
            if high_conf_patterns:
                for pattern in high_conf_patterns[:2]:  # Top 2 patterns
                    insights.append(RealTimeInsight(
                        timestamp=datetime.now(),
                        insight_type="HIGH_CONFIDENCE_PATTERN",
                        message=f"High confidence {pattern.pattern_type.value} detected",
                        confidence=pattern.confidence,
                        urgency="MEDIUM",
                        data={
                            "pattern_type": pattern.pattern_type.value,
                            "expected_profit": pattern.expected_profit,
                            "risk_reward": pattern.risk_reward
                        }
                    ))
            
            # Trading opportunities
            if result.trading_opportunities:
                best_opportunity = max(result.trading_opportunities, 
                                     key=lambda o: o.pattern.expected_profit)
                insights.append(RealTimeInsight(
                    timestamp=datetime.now(),
                    insight_type="TRADING_OPPORTUNITY",
                    message=f"Best opportunity: {best_opportunity.symbol} {best_opportunity.pattern.pattern_type.value}",
                    confidence=best_opportunity.pattern.confidence,
                    urgency="MEDIUM",
                    data={
                        "symbol": best_opportunity.symbol,
                        "expected_profit": best_opportunity.pattern.expected_profit,
                        "position_size": best_opportunity.position_size
                    }
                ))
            
            # Volume insights
            total_volume = sum(opt.volume for opt in market_data)
            if total_volume > 10000000:  # High volume threshold
                insights.append(RealTimeInsight(
                    timestamp=datetime.now(),
                    insight_type="HIGH_VOLUME",
                    message=f"Unusually high volume detected: {total_volume:,.0f}",
                    confidence=0.8,
                    urgency="LOW",
                    data={"total_volume": total_volume}
                ))
            
        except Exception as e:
            self.logger.error(f"❌ Failed to generate insights: {str(e)}")
        
        return insights
    
    def _generate_alerts(self, result: DeepAnalysisResult, _market_data: List[OptionsData]) -> List[MarketAlert]:
        """Generate real-time alerts"""
        alerts = []
        
        try:
            # Critical regime alerts
            if result.market_snapshot.regime.value in ["EXTREME", "CRISIS"]:
                alerts.append(MarketAlert(
                    timestamp=datetime.now(),
                    alert_type="MARKET_REGIME",
                    symbol="MARKET",
                    message=f"Market in {result.market_snapshot.regime.value} regime - Trading suspended",
                    severity="CRITICAL",
                    action_required=True,
                    data={"regime": result.market_snapshot.regime.value}
                ))
            
            # High VIX alerts
            if result.market_snapshot.vix_level > 35:
                alerts.append(MarketAlert(
                    timestamp=datetime.now(),
                    alert_type="HIGH_VOLATILITY",
                    symbol="MARKET",
                    message=f"High volatility detected: VIX {result.market_snapshot.vix_level:.1f}",
                    severity="WARNING",
                    action_required=False,
                    data={"vix_level": result.market_snapshot.vix_level}
                ))
            
            # Pattern-specific alerts
            critical_patterns = [p for p in result.detected_patterns 
                               if p.confidence > 0.9 and p.expected_profit > 0.15]
            
            for pattern in critical_patterns:
                alerts.append(MarketAlert(
                    timestamp=datetime.now(),
                    alert_type="CRITICAL_PATTERN",
                    symbol="PATTERN",
                    message=f"Critical {pattern.pattern_type.value} pattern detected",
                    severity="CRITICAL",
                    action_required=True,
                    data={
                        "pattern_type": pattern.pattern_type.value,
                        "confidence": pattern.confidence,
                        "expected_profit": pattern.expected_profit
                    }
                ))
            
        except Exception as e:
            self.logger.error(f"❌ Failed to generate alerts: {str(e)}")
        
        return alerts
    
    def _update_performance_metrics(self, result: DeepAnalysisResult, cycle_time: float):
        """Update performance metrics"""
        try:
            self.performance_metrics['total_analyses'] += 1
            self.performance_metrics['total_patterns'] += len(result.detected_patterns)
            self.performance_metrics['total_opportunities'] += len(result.trading_opportunities)
            
            # Update average analysis time
            total_time = self.performance_metrics['avg_analysis_time'] * (self.performance_metrics['total_analyses'] - 1)
            self.performance_metrics['avg_analysis_time'] = (total_time + cycle_time) / self.performance_metrics['total_analyses']
            
            # Calculate success rate (patterns with high confidence)
            high_conf_patterns = len([p for p in result.detected_patterns if p.confidence > 0.7])
            if self.performance_metrics['total_patterns'] > 0:
                self.performance_metrics['success_rate'] = high_conf_patterns / self.performance_metrics['total_patterns']
            
        except Exception as e:
            self.logger.error(f"❌ Failed to update performance metrics: {str(e)}")
    
    def _log_cycle_summary(self, result: DeepAnalysisResult, insights: List[RealTimeInsight], alerts: List[MarketAlert]):
        """Log cycle summary"""
        try:
            self.logger.info(f"📊 CYCLE SUMMARY:")
            self.logger.info(f"   Regime: {result.market_snapshot.regime.value}")
            self.logger.info(f"   VIX: {result.market_snapshot.vix_level:.1f}")
            self.logger.info(f"   Patterns: {len(result.detected_patterns)}")
            self.logger.info(f"   Opportunities: {len(result.trading_opportunities)}")
            self.logger.info(f"   Insights: {len(insights)}")
            self.logger.info(f"   Alerts: {len(alerts)}")
            
            # Show critical alerts
            critical_alerts = [a for a in alerts if a.severity == "CRITICAL"]
            if critical_alerts:
                self.logger.warning(f"🚨 CRITICAL ALERTS:")
                for alert in critical_alerts:
                    self.logger.warning(f"     • {alert.message}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to log cycle summary: {str(e)}")
    
    def _show_realtime_dashboard(self):
        """Show real-time dashboard"""
        try:
            self.logger.info("📈 REAL-TIME DASHBOARD")
            self.logger.info("-" * 40)
            self.logger.info(f"Market Regime: {self.market_state['current_regime']}")
            self.logger.info(f"Risk Level: {self.market_state['risk_level']}")
            self.logger.info(f"Trading Allowed: {self.market_state['trading_allowed']}")
            self.logger.info(f"Dominant Flow: {self.market_state['dominant_flow']}")
            self.logger.info(f"VIX Level: {self.market_state['vix_level']:.1f}")
            self.logger.info("-" * 40)
            self.logger.info(f"Total Analyses: {self.performance_metrics['total_analyses']}")
            self.logger.info(f"Avg Analysis Time: {self.performance_metrics['avg_analysis_time']:.2f}s")
            self.logger.info(f"Success Rate: {self.performance_metrics['success_rate']:.1%}")
            self.logger.info("")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to show dashboard: {str(e)}")
    
    def get_latest_insights(self, limit: int = 10) -> List[RealTimeInsight]:
        """Get latest insights"""
        return self.insights_history[-limit:] if self.insights_history else []
    
    def get_latest_alerts(self, limit: int = 10) -> List[MarketAlert]:
        """Get latest alerts"""
        return self.alerts_history[-limit:] if self.alerts_history else []

# Global instance
realtime_analyzer = RealTimeAnalyzer()

async def main():
    """Main entry point for real-time analysis"""
    try:
        await realtime_analyzer.start_realtime_analysis()
    except KeyboardInterrupt:
        print("\n🛑 Real-time analysis stopped by user")
    except Exception as e:
        print(f"❌ Real-time analysis failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
