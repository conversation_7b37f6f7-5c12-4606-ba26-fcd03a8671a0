#!/usr/bin/env python3
"""
Deep Analysis Demo Runner
========================

Comprehensive demonstration of the NSE Options Deep Analysis System.
This script showcases all capabilities using real market data.

DEMO MODES:
===========

1. QUICK DEMO (5 minutes)
   - Basic pattern detection
   - Market analysis overview
   - Key insights display

2. COMPREHENSIVE DEMO (15 minutes)
   - Full deep analysis
   - Real-time monitoring
   - Performance analytics
   - Trading opportunities

3. STRESS TEST (30 minutes)
   - Continuous analysis cycles
   - Performance benchmarking
   - System validation
   - Comprehensive reporting

4. INTERACTIVE MODE
   - User-controlled analysis
   - Custom parameter testing
   - Real-time insights
   - Manual pattern exploration

Built for REAL NSE market conditions with comprehensive validation.
"""

import asyncio
import logging
import sys
import os
import argparse
from datetime import datetime
from typing import Dict, Any

# Set UTF-8 encoding for Windows console
if sys.platform == "win32":
    os.environ["PYTHONIOENCODING"] = "utf-8"

# Import demo modules
from deep_analysis_demo import DeepAnalysisDemo
from realtime_analysis import realtime_analyzer
from test_deep_analysis import DeepAnalysisTestSuite
from utils.enhanced_logging import enhanced_logger

logger = logging.getLogger(__name__)

class DemoRunner:
    """
    Main demo runner for the deep analysis system
    """
    
    def __init__(self):
        self.logger = enhanced_logger.logger
        self.demo_modes = {
            'quick': self._run_quick_demo,
            'comprehensive': self._run_comprehensive_demo,
            'stress': self._run_stress_test,
            'interactive': self._run_interactive_mode,
            'test': self._run_test_suite
        }
    
    async def run_demo(self, mode: str = 'comprehensive', **kwargs):
        """Run demo in specified mode"""
        try:
            self.logger.info("🚀 NSE OPTIONS DEEP ANALYSIS SYSTEM DEMO")
            self.logger.info("=" * 80)
            self.logger.info("Advanced Options Manipulation Detection & Trading System")
            self.logger.info("Real Market Data Analysis with Institutional Flow Detection")
            self.logger.info("=" * 80)
            self.logger.info(f"Demo Mode: {mode.upper()}")
            self.logger.info(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info("=" * 80)
            
            if mode in self.demo_modes:
                await self.demo_modes[mode](**kwargs)
            else:
                self.logger.error(f"❌ Unknown demo mode: {mode}")
                self._show_available_modes()
            
            self.logger.info("=" * 80)
            self.logger.info("✅ DEMO COMPLETED SUCCESSFULLY")
            self.logger.info("=" * 80)
            
        except KeyboardInterrupt:
            self.logger.info("\n🛑 Demo stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Demo failed: {str(e)}")
            raise
    
    async def _run_quick_demo(self, **kwargs):
        """Run quick 5-minute demo"""
        try:
            self.logger.info("⚡ QUICK DEMO (5 minutes)")
            self.logger.info("Showcasing core capabilities with real market data")
            self.logger.info("-" * 60)
            
            demo = DeepAnalysisDemo()
            
            # Initialize system
            await demo._initialize_system()
            
            # Run basic analysis
            self.logger.info("🧠 Running basic deep analysis...")
            await demo._demo_analysis_depths()
            
            # Show performance overview
            self.logger.info("📊 Performance overview...")
            await demo._demo_performance_analytics()
            
            # Generate quick report
            await demo._generate_demo_report()
            
            self.logger.info("✅ Quick demo completed")
            
        except Exception as e:
            self.logger.error(f"❌ Quick demo failed: {str(e)}")
            raise
    
    async def _run_comprehensive_demo(self, **kwargs):
        """Run comprehensive 15-minute demo"""
        try:
            self.logger.info("🎯 COMPREHENSIVE DEMO (15 minutes)")
            self.logger.info("Full system demonstration with all features")
            self.logger.info("-" * 60)
            
            demo = DeepAnalysisDemo()
            
            # Run full demo
            await demo.run_comprehensive_demo()
            
            # Add real-time analysis sample
            self.logger.info("⚡ Real-time analysis sample...")
            
            # Run 3 cycles of real-time analysis
            for cycle in range(3):
                self.logger.info(f"🔄 Real-time cycle {cycle + 1}/3")
                await realtime_analyzer._run_analysis_cycle()
                
                if cycle < 2:
                    await asyncio.sleep(30)  # 30 seconds between cycles
            
            self.logger.info("✅ Comprehensive demo completed")
            
        except Exception as e:
            self.logger.error(f"❌ Comprehensive demo failed: {str(e)}")
            raise
    
    async def _run_stress_test(self, **kwargs):
        """Run 30-minute stress test"""
        try:
            self.logger.info("💪 STRESS TEST (30 minutes)")
            self.logger.info("Continuous analysis with performance monitoring")
            self.logger.info("-" * 60)
            
            # Run test suite first
            self.logger.info("🧪 Running validation tests...")
            test_suite = DeepAnalysisTestSuite()
            await test_suite.run_all_tests()
            
            # Run continuous analysis for 30 minutes
            self.logger.info("⚡ Starting continuous analysis...")
            
            start_time = datetime.now()
            cycle_count = 0
            
            while (datetime.now() - start_time).total_seconds() < 1800:  # 30 minutes
                cycle_count += 1
                self.logger.info(f"🔄 Stress test cycle {cycle_count}")
                
                # Run analysis cycle
                await realtime_analyzer._run_analysis_cycle()
                
                # Show progress every 10 cycles
                if cycle_count % 10 == 0:
                    elapsed = (datetime.now() - start_time).total_seconds()
                    self.logger.info(f"📊 Progress: {elapsed/60:.1f} minutes, {cycle_count} cycles completed")
                
                await asyncio.sleep(60)  # 1 minute between cycles
            
            self.logger.info(f"✅ Stress test completed: {cycle_count} cycles in 30 minutes")
            
        except Exception as e:
            self.logger.error(f"❌ Stress test failed: {str(e)}")
            raise
    
    async def _run_interactive_mode(self, **kwargs):
        """Run interactive demo mode"""
        try:
            self.logger.info("🎮 INTERACTIVE MODE")
            self.logger.info("User-controlled analysis and exploration")
            self.logger.info("-" * 60)
            
            demo = DeepAnalysisDemo()
            await demo._initialize_system()
            
            while True:
                print("\n" + "=" * 50)
                print("INTERACTIVE DEMO MENU")
                print("=" * 50)
                print("1. Run Deep Analysis")
                print("2. Real-time Analysis Cycle")
                print("3. Performance Analytics")
                print("4. System Status")
                print("5. Run Tests")
                print("6. Exit")
                print("=" * 50)
                
                try:
                    choice = input("Select option (1-6): ").strip()
                    
                    if choice == '1':
                        self.logger.info("🧠 Running deep analysis...")
                        await demo._demo_analysis_depths()
                    
                    elif choice == '2':
                        self.logger.info("⚡ Running real-time analysis cycle...")
                        await realtime_analyzer._run_analysis_cycle()
                    
                    elif choice == '3':
                        self.logger.info("📊 Showing performance analytics...")
                        await demo._demo_performance_analytics()
                    
                    elif choice == '4':
                        self.logger.info("🔍 System status...")
                        realtime_analyzer._show_realtime_dashboard()
                    
                    elif choice == '5':
                        self.logger.info("🧪 Running tests...")
                        test_suite = DeepAnalysisTestSuite()
                        await test_suite.run_all_tests()
                    
                    elif choice == '6':
                        self.logger.info("👋 Exiting interactive mode...")
                        break
                    
                    else:
                        print("❌ Invalid choice. Please select 1-6.")
                
                except KeyboardInterrupt:
                    self.logger.info("\n🛑 Interactive mode stopped by user")
                    break
                except Exception as e:
                    self.logger.error(f"❌ Error in interactive mode: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"❌ Interactive mode failed: {str(e)}")
            raise
    
    async def _run_test_suite(self, **kwargs):
        """Run comprehensive test suite"""
        try:
            self.logger.info("🧪 TEST SUITE")
            self.logger.info("Comprehensive system validation")
            self.logger.info("-" * 60)
            
            test_suite = DeepAnalysisTestSuite()
            await test_suite.run_all_tests()
            
        except Exception as e:
            self.logger.error(f"❌ Test suite failed: {str(e)}")
            raise
    
    def _show_available_modes(self):
        """Show available demo modes"""
        self.logger.info("📋 AVAILABLE DEMO MODES:")
        self.logger.info("  quick        - Quick 5-minute demo")
        self.logger.info("  comprehensive - Full 15-minute demo")
        self.logger.info("  stress       - 30-minute stress test")
        self.logger.info("  interactive  - User-controlled mode")
        self.logger.info("  test         - Run test suite only")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="NSE Options Deep Analysis System Demo",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Demo Modes:
  quick        Quick 5-minute demonstration
  comprehensive Full 15-minute demonstration (default)
  stress       30-minute stress test with continuous analysis
  interactive  User-controlled interactive mode
  test         Run comprehensive test suite only

Examples:
  python run_deep_analysis_demo.py
  python run_deep_analysis_demo.py --mode quick
  python run_deep_analysis_demo.py --mode interactive
  python run_deep_analysis_demo.py --mode stress
        """
    )
    
    parser.add_argument(
        '--mode', '-m',
        choices=['quick', 'comprehensive', 'stress', 'interactive', 'test'],
        default='comprehensive',
        help='Demo mode to run (default: comprehensive)'
    )
    
    parser.add_argument(
        '--log-level', '-l',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    parser.add_argument(
        '--no-color',
        action='store_true',
        help='Disable colored output'
    )
    
    args = parser.parse_args()
    
    # Configure logging
    if args.log_level:
        enhanced_logger.logger.setLevel(getattr(logging, args.log_level))
    
    try:
        # Show banner
        print("\n" + "=" * 80)
        print("🚀 NSE OPTIONS DEEP ANALYSIS SYSTEM DEMO")
        print("=" * 80)
        print("Advanced Options Manipulation Detection & Trading System")
        print("Real Market Data Analysis with Institutional Flow Detection")
        print("=" * 80)
        print(f"Mode: {args.mode.upper()}")
        print(f"Log Level: {args.log_level}")
        print("=" * 80)
        
        # Run demo
        runner = DemoRunner()
        asyncio.run(runner.run_demo(args.mode))
        
    except KeyboardInterrupt:
        print("\n🛑 Demo stopped by user")
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
